"use client";

import FacebookIcon from "@/components/icons/FacebookIcon";
import InstagramIcon from "@/components/icons/InstagramIcon";
import LinkedInIcon from "@/components/icons/LinkedInIcon";
import { cn } from "@/lib/utils";
import { Link } from "@/i18n/routing";
import { useSlideAnimation } from "../animations";

export const socialsItems = [
  {
    href: "https://www.facebook.com/p/Pedro-Yaba-***************/",
    Icon: FacebookIcon,
  },
  {
    href: "https://www.instagram.com/pedro_yaba/",
    Icon: InstagramIcon,
  },
  {
    href: "https://www.linkedin.com/in/pedro-yaba-260bab330/?originalSubdomain=ao",
    Icon: LinkedInIcon,
  },
];

const SocialButtons = ({ className }: { className?: string }) => {
  const { scope } = useSlideAnimation<HTMLSpanElement>({
    selector: "a",
    delay: 0.5,
    duration: 0.5,
    inViewProps: { margin: "10% 0px" },
  });

  return (
    <span ref={scope} className={cn("flex items-center gap-4", className)}>
      {socialsItems.map(({ href, Icon }, index) => {
        return (
          <Link key={index} href={href} className="opacity-0">
            <Icon className={"size-5"} strokeWidth={1.25} />
          </Link>
        );
      })}
    </span>
  );
};

export default SocialButtons;

"use client";

import Image from "next/image";
import SignatureImg from "@/assets/imgs/signature.svg";
import { cn } from "@/lib/utils";
import AboutContent from "./AboutContent";
import { useState } from "react";
import { linksObj } from "../links";
import MainImage from "@/assets/imgs/pedro-yaba.png";

import {
  useFadeAnimation,
  useRevel,
  useSlideAnimation,
  useTextReveal,
} from "../animations";

import { useTranslations } from "next-intl";
import { pedroBgStyles } from "../bg-utils";

const AboutSection = ({ className }: { className?: string }) => {
  const t = useTranslations("Links.about");

  const [isExpanded, setIsExpanded] = useState(false);

  const { scope: logoHScope } = useFadeAnimation<HTMLDivElement>({
    delay: 1,
  });
  const { scope: titleScope } = useSlideAnimation<HTMLHeadingElement>({
    delay: 1,
    selector: "span",
  });

  const { scope: mainImageScope } = useSlideAnimation<HTMLImageElement>({
    direction: "bottom",
  });

  const { scope: textScope } = useTextReveal<HTMLHeadingElement>({
    selector: "span",
    duration: 0.3,
    staggerDelay: 0.1,
  });
  const { scope: contentScope } = useRevel<HTMLElement>();

  const { scope: signImageScope } = useFadeAnimation<HTMLImageElement>({});

  const toggleExpand = () => setIsExpanded(!isExpanded);

  return (
    <section
      id={linksObj.about.href.replace("#", "")}
      className={cn(
        "relative flex flex-col gap-10 bg-background transition-all duration-300",
        className,
        isExpanded
          ? "!mx-0 rounded-t-none p-10 lg:p-20"
          : "rounded-t-[3rem] p-10 sm:rounded-t-[6rem]",
      )}
    >
      <div
        ref={logoHScope}
        className="opacity-0"
        style={{
          ...pedroBgStyles,
          maskImage: `url(${SignatureImg.src})`,
          maskRepeat: "no-repeat",
          maskSize: "100% 100%",
        }}
      >
        <Image
          ref={signImageScope}
          src={SignatureImg}
          alt="Signature"
          width={500}
          height={100}
          className="invisible h-auto w-full"
          unoptimized
        />
      </div>
      <Image
        ref={mainImageScope}
        src={MainImage}
        alt="Main Image"
        width={200}
        height={800}
        className="absolute right-20 top-[-32rem] h-[200vh] max-h-[100rem] w-auto object-contain opacity-0 max-lg:hidden"
        unoptimized
        loading="eager"
      />
      <section className="flex items-center gap-10 overflow-hidden max-sm:flex-col">
        <article className="flex flex-col gap-5">
          <h3
            ref={titleScope}
            className="w-fit overflow-hidden text-3xl font-bold lowercase text-primary-foreground lg:text-6xl"
          >
            <span className="inline-block opacity-0">{t("label")}</span>
          </h3>
          <p
            ref={textScope}
            className="text-base font-bold text-primary-foreground sm:ml-10 sm:max-w-[40ch] lg:text-xl"
            dangerouslySetInnerHTML={{ __html: t("description") }}
          ></p>
        </article>
        {/* <video
          ref={videoScope}
          className="w-full rounded-xl sm:w-[50%]"
          controls
        >
          <source src="/vids/about.mp4" type="video/mp4" />
        </video> */}
      </section>
      <AboutContent
        className="opacity-0"
        ref={contentScope}
        isExpanded={isExpanded}
        toggleExpand={toggleExpand}
      />
    </section>
  );
};

export default AboutSection;

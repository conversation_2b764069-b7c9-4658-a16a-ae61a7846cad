"use client";
import { POSTS_QUERYResult } from "@/sanity/gen-types";
import Script from "next/script";

const Posts = ({ posts }: { posts: POSTS_QUERYResult }) => {
  return (
    <ul className="mx-auto grid w-full max-w-5xl grid-cols-1 place-items-center">
      {posts.map((post) => (
        <li key={post._id}>
          {post.embed && (
            <div dangerouslySetInnerHTML={{ __html: post.embed }}></div>
          )}
        </li>
      ))}
      <Script
        src="https://www.instagram.com/embed.js"
        strategy="lazyOnload"
        onReady={() => window.instgrm && window.instgrm.Embeds.process()}
      />
    </ul>
  );
};

export default Posts;

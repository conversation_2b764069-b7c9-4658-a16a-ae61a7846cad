import { DocumentTextIcon } from "@sanity/icons";
import { defineArrayMember, defineField, defineType } from "sanity";

export const postType = defineType({
  name: "post",
  title: "Postagem",
  type: "document",
  icon: DocumentTextIcon,
  fields: [
    defineField({
      name: "title",
      type: "string",
      validation: (Rule) => Rule.required().error("O título é obrigatório"),
    }),
    defineField({
      name: "embed",
      type: "string",
      title: "Postagem embed",
      description: "Aqui você pode colar o código do embed",
      validation: (Rule) => Rule.required().error("O embed é obrigatório"),
    }),
    defineField({
      name: "type",
      type: "string",
      validation: (Rule) => Rule.required().error("A plataforma é obrigatória"),
      title: "Plataforma",
      description: "Selecione a plataforma do postagem",
      options: {
        list: [
          { title: "Instagram", value: "instagram" },
          { title: "Youtube", value: "youtube" },
          { title: "Linkedin", value: "linkedin" },
        ],
        layout: "dropdown",
      },
    }),
  ],
  preview: {
    select: {
      title: "title",
    },
  },
});

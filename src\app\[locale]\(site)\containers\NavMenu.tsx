"use client";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import SocialButtons from "./SocialButtons";
import { Menu } from "lucide-react";
import { Link } from "@/i18n/routing";
import { navLinks } from "../links";
import { lenisScrollTo, useLenis } from "@/lib/lenis";

const NavMenu = ({ className }: { className?: string }) => {
  const lenis = useLenis();
  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        className={cn(
          "flex w-fit items-center gap-x-2 text-xs uppercase outline-primary transition-all duration-200 ease-in",
          className,
        )}
      >
        <Menu className="size-6" />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Menu</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {navLinks.map(({ href, label }) => {
          return (
            <DropdownMenuItem key={href}>
              <Link
                onClick={() => lenisScrollTo({ href, lenis })}
                key={href}
                href={href}
                className="font-medium lowercase text-foreground first-of-type:text-primary-foreground hover:text-primary"
              >
                {label}
              </Link>
            </DropdownMenuItem>
          );
        })}
        <DropdownMenuSeparator />
        <SocialButtons className="flex-wrap p-2" />
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default NavMenu;

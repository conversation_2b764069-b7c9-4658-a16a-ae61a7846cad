import "../../globals.css";
import { cn } from "@/lib/utils";
import { mainFont } from "../../fonts";
import Header from "./containers/Header";
import { ReactLenis } from "@/lib/lenis";
import Footer from "./containers/Footer";

import { NextIntlClientProvider } from "next-intl";
import {
  getMessages,
  getTranslations,
  setRequestLocale,
} from "next-intl/server";
import { routing } from "@/i18n/routing";
import { GoogleMapsEmbed } from "@next/third-parties/google";

export async function generateMetadata(props: {
  params: Promise<{ locale: string }>;
}) {
  const params = await props.params;

  const { locale } = params;

  const t = await getTranslations({ locale, namespace: "Metadata" });

  return {
    title: t("title"),
    description: t("description"),
  };
}

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

export default async function RootLayout(
  props: Readonly<{
    children: React.ReactNode;
    params: { locale: string };
  }>,
) {
  const params = await props.params;

  const { locale } = params;

  const { children } = props;

  setRequestLocale(locale);
  const messages = await getMessages();

  return (
    <ReactLenis root>
      <html lang={locale} className="min-h-screen">
        <body
          className={cn(
            "flex min-h-screen flex-col bg-gradient-to-b from-primary to-primary-foreground font-sans antialiased",
            mainFont.variable,
            // titleFont.variable,
          )}
        >
          <NextIntlClientProvider messages={messages}>
            <Header />
            {children}
            <Footer
              mapChildren={
                <GoogleMapsEmbed
                  apiKey={process.env.GOOGLE_MAPS_API_KEY || ""}
                  height={350}
                  width="100%"
                  mode="place"
                  q="259R+VG Luanda"
                  zoom="20"
                  language="pt"
                  maptype="satellite"
                  style="position: relative; width: 100%; height: 100%; opacity: 0; z-index: 10;"
                />
              }
            />
          </NextIntlClientProvider>
        </body>
      </html>
    </ReactLenis>
  );
}

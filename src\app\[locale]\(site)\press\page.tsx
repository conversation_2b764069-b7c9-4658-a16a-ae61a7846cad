import { POSTS_QUERYResult } from "@/sanity/gen-types";
import { sanityFetch } from "@/sanity/lib/client";
import { POSTS_QUERY } from "@/sanity/lib/queries";
import Posts from "./Posts";

const PressPage = async () => {
  const posts = await sanityFetch<POSTS_QUERYResult>({
    query: POSTS_QUERY,
  });

  return (
    <main className="flex flex-col items-center justify-center px-5 py-32 sm:px-20 sm:py-40">
      <Posts posts={posts} />
    </main>
  );
};

export default PressPage;

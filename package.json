{"name": "pedro-yaba", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@gsap/react": "^2.1.1", "@next/third-parties": "15.0.3", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@sanity/icons": "^3.4.0", "@sanity/image-url": "^1.1.0", "@sanity/vision": "^3.64.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "embla-carousel-react": "^8.3.1", "gsap": "^3.12.5", "lenis": "^1.1.16", "lucide-react": "^0.456.0", "marked": "^15.0.0", "motion": "^11.11.14", "next": "15.1.6", "next-intl": "^3.25.0", "next-sanity": "^9.8.12", "photoswipe": "^5.4.4", "react": "19.0.0", "react-dom": "19.0.0", "sanity": "^3.64.0", "styled-components": "^6.1.13", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "eslint": "^9.14.0", "eslint-config-next": "15.0.3", "postcss": "^8.4.49", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^3.4.14", "typescript": "^5.6.3"}, "pnpm": {"overrides": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}}}
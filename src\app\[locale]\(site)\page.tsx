import { getTranslations, setRequestLocale } from "next-intl/server";
import AboutSection from "./about/AboutSection";
import { pedroBgStyles } from "./bg-utils";
import Container from "./containers/Container";
import GallerySection from "./containers/GallerySection";
import { getLinks } from "./links";
import InfoCarousel from "./containers/InfoCarrousel";
import PedroImage from "@/assets/imgs/pedro-yaba.png";
import Image from "next/image";
import PressSection from "./press/PressSection";

export default async function Home(props: {
  params: Promise<{ locale: string }>;
}) {
  const params = await props.params;
  const { locale } = params;
  setRequestLocale(locale);

  const t = await getTranslations("Links");
  const links = Object.values(getLinks(t));



  return (
    <main className="relative grid justify-items-center">
      <div
        style={pedroBgStyles}
        className="relative pt-[10vh] before:absolute before:left-0 before:top-0 before:h-full before:w-full before:backdrop-blur-[10px] sm:pt-[20vh]"
      >
        <Image
          src={PedroImage}
          alt="Main Image"
          width={500}
          height={100}
          className="relative mb-[-30vh] h-auto max-h-[80vh] w-full object-contain px-5 lg:hidden"
          quality={80}
          loading="eager"
        />
        <Container>
          <InfoCarousel className="mb-10 px-10 sm:mb-20" infosList={links} />
        </Container>
        <Container>
          <AboutSection className="mx-5 lg:mx-10" />
        </Container>
      </div>
      <Container>
        <GallerySection />
      </Container>
      <Container>
        <PressSection className="p-10" />
      </Container>
    </main>
  );
}
